import { useState } from 'react';
import { User } from '@/types/project';
import { Di<PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

export function SupervisorAssignDialog({
  open,
  onOpenChange,
  designers,
  onConfirm,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  designers: User[];
  onConfirm: (assigneeId: string) => void;
}) {
  const [assignee, setAssignee] = useState('');

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assign Supervisor</DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          <div>
            <label className="text-sm">Assign to</label>
            <Select value={assignee} onValueChange={setAssignee}>
              <SelectTrigger>
                <SelectValue placeholder="Select a supervisor" />
              </SelectTrigger>
              <SelectContent>
                {designers.map((u) => (
                  <SelectItem key={u.id} value={u.id}>{u.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="text-sm text-muted-foreground">
            The assigned supervisor will be able to select their subtasks after assignment.
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
            <Button disabled={!assignee} onClick={() => { onConfirm(assignee); onOpenChange(false); setAssignee(''); }}>Assign</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

