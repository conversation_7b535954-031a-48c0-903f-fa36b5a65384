import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel } from "@/components/ui/alert-dialog";
import { User } from "@/types/project";
import { User as UserIcon } from "lucide-react";

export function SupervisorAssignInline({ users = [], projectId, onAssign }: { users: User[]; projectId: string; onAssign?: (projectId: string, assigneeId: string, phases?: string[]) => void; }) {
  const [open, setOpen] = useState(false);
  const [assignee, setAssignee] = useState("");

  const confirm = () => {
    if (!assignee) return;
    onAssign?.(projectId, assignee);
    setOpen(false);
    setAssignee("");
  };

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="default" size="sm">
          <UserIcon className="h-4 w-4 mr-1" /> Assign / Reassign
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Assign Supervisor</AlertDialogTitle>
          <AlertDialogDescription>
            Select a supervisor to assign. They will be able to select their subtasks after assignment.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="space-y-3 py-2">
          <div>
            <label className="text-sm">Assign to</label>
            <Select value={assignee} onValueChange={setAssignee}>
              <SelectTrigger>
                <SelectValue placeholder="Select a supervisor" />
              </SelectTrigger>
              <SelectContent>
                {users.filter(u => u.role === 'supervisor').map(u => (
                  <SelectItem key={u.id} value={u.id}>{u.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <Button onClick={confirm} disabled={!assignee}>Confirm</Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

