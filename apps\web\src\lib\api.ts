import { Project, ProjectStatus, SalesWonSubStatus, UserRole } from "@/types/project";

// TODO: Remove old auth system - Clerk handles authentication automatically
// Authentication is now handled by Clerk session cookies
const withAuth = (init?: RequestInit): RequestInit => {
  const headers = new Headers(init?.headers || {});
  // Clerk session will be automatically included in requests
  return { ...init, headers, credentials: 'include' };
};

const json = (res: Response) => {
  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json();
};

// Case API response types (minimal for UI needs)
export type CaseListItem = {
  caseId: string;
  title: string;
  client: string;
  salesAmount: number;
  lastUpdateAt: string;
  roles: {
    sales?: { id: string; status: string; assignedTo: string | null; assignedToName?: string | null; createdAt: string; updatedAt: string } | null;
    designer?: { id: string; status: string; assignedTo: string | null; assignedToName?: string | null; createdAt: string; updatedAt: string } | null;
    supervisor?: { id: string; status: string; assignedTo: string | null; assignedToName?: string | null; createdAt: string; updatedAt: string; supervisorSelectedPhases?: string[]; supervisorPhaseDates?: Record<string, string> } | null;
  };
};

export type PaginatedCases = {
  items: CaseListItem[];
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
};

export type CaseSummary = {
  caseId: string;
  title: string;
  client: string;
  salesAmount: number;
  roles: {
    sales: { id?: string; status?: string; assignedTo?: string | null } | null;
    designer: { id?: string; status?: string; assignedTo?: string | null } | null;
    supervisor: { id?: string; status?: string; assignedTo?: string | null } | null;
  };
};

export type CaseHistory = {
  caseId: string;
  events: Array<{
    id: string;
    caseId: string;
    role: 'sales' | 'designer' | 'supervisor';
    type: string;
    timestamp: string;
    user?: { id: string | null; name?: string | null } | null;
    sourceId: string;
    payload?: {
      kind?: string;
      phase?: string;
      completedAt?: string;
      status?: string;
      selected?: string[];
      from?: string;
      to?: string;
      fromUserId?: string;
      toUserId?: string;
      fromStatus?: string;
      toStatus?: string;
      salesSubStatusFrom?: string;
      salesSubStatusTo?: string;
      changes?: Array<{ field: string; from: any; to: any }>;
    };
  }>;
};

export const Api = {
  // TODO: Remove setAuth - authentication is now handled by Clerk
  setAuth(userId: string, role: UserRole) {
    console.warn('setAuth is deprecated - authentication is handled by Clerk');
    // No-op for backward compatibility
  },
  async listUsers(): Promise<Array<{id: string; name: string; email: string; role: string}>> {
    return fetch(`/api/users`, withAuth()).then(json);
  },
  async listProjects(): Promise<Project[]> {
    return fetch(`/api/projects`, withAuth()).then(json);
  },
  async createProject(data: { title: string; client: string; salesAmount: number }): Promise<Project> {
    return fetch(`/api/projects`, withAuth({
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: data.title,
        client: data.client,
        salesAmount: data.salesAmount,
      }),
    })).then(json);
  },
  async updateProject(id: string, data: Partial<Pick<Project, 'title' | 'client' | 'assignedTo' | 'remarks'>> & { expiredDate?: string, dueDate?: string, supervisorSelectedPhases?: string[], supervisorPhaseDates?: Record<string, string> }): Promise<Project> {
    return fetch(`/api/projects/${id}`, withAuth({
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    })).then(json);
  },
  async updateProjectStatus(
    id: string,
    payload: { status: ProjectStatus; salesSubStatus?: SalesWonSubStatus; isRevision?: boolean; phaseCompletedAt?: string; phaseKey?: string; isPhaseCompletion?: boolean }
  ): Promise<Project> {
    return fetch(`/api/projects/${id}/status`, withAuth({
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    })).then(json);
  },
  async assignSales(id: string, assignedTo: string): Promise<Project> {
    return fetch(`/api/projects/${id}/assign/sales`, withAuth({
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ assignedTo }),
    })).then(json);
  },
  async assignDesigner(id: string, assignedTo: string): Promise<Project> {
    return fetch(`/api/projects/${id}/assign/designer`, withAuth({
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ assignedTo }),
    })).then(json);
  },
  async assignSupervisor(id: string, assignedTo: string): Promise<Project> {
    return fetch(`/api/projects/${id}/assign/supervisor`, withAuth({
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ assignedTo }),
    })).then(json);
  },
  async selectSupervisorSubtasks(id: string, selectedTasks: string[]): Promise<Project> {
    return fetch(`/api/projects/${id}/supervisor/subtasks`, withAuth({
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ selectedTasks }),
    })).then(json);
  },
  async getProject(id: string): Promise<Project> {
    return fetch(`/api/projects/${id}`, withAuth()).then(json);
  },

  // New Case History APIs
  async listCases(params?: { page?: number; pageSize?: number; sort?: 'lastUpdateAt'; sortDir?: 'asc'|'desc'; role?: 'sales'|'designer'|'supervisor'; status?: string; assignedTo?: string; dateFrom?: string; dateTo?: string; }): Promise<PaginatedCases> {
    const qs = new URLSearchParams();
    if (params?.page) qs.set('page', String(params.page));
    if (params?.pageSize) qs.set('pageSize', String(params.pageSize));
    if (params?.sort) qs.set('sort', params.sort);
    if (params?.sortDir) qs.set('sortDir', params.sortDir);
    if (params?.role) qs.set('role', params.role);
    if (params?.status) qs.set('status', params.status);
    if (params?.assignedTo) qs.set('assignedTo', params.assignedTo);
    if (params?.dateFrom) qs.set('dateFrom', params.dateFrom);
    if (params?.dateTo) qs.set('dateTo', params.dateTo);
    const url = qs.toString() ? `/api/cases?${qs.toString()}` : '/api/cases';
    return fetch(url, withAuth()).then(json);
  },
  async getCase(caseId: string): Promise<CaseSummary> {
    return fetch(`/api/cases/${caseId}`, withAuth()).then(json);
  },
  async getCaseHistory(caseId: string): Promise<CaseHistory> {
    return fetch(`/api/cases/${caseId}/history`, withAuth()).then(json);
  },
  async transitionSupervisorPhase(id: string, data: { phaseKey: string, to: 'not_started'|'booked'|'pending'|'in_progress'|'complete'|'defect', at?: string }): Promise<Project> {
    return fetch(`/api/projects/${id}/supervisor-phase/transition`, withAuth({
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    })).then(json);
  },
};

