import { useState } from 'react';
import { SUPERVISOR_SUBTASK_LABELS, SupervisorSubTask } from '@/types/project';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

const SUPERVISOR_PHASES = [
  'floor_protection', 'plaster_ceiling', 'spc', 'first_painting',
  'carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install',
  'glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others',
  'plumbing', 'cleaning', 'defects'
] as const;

export function SubtaskSelectionDialog({
  open,
  onOpenChange,
  onConfirm,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  onConfirm: (selectedTasks: string[]) => void;
}) {
  const [selected, setSelected] = useState<string[]>([]);

  const toggle = (phase: string) => {
    setSelected((prev) => prev.includes(phase) ? prev.filter(p => p !== phase) : [...prev, phase]);
  };

  const handleConfirm = () => {
    if (selected.length === 0) return;
    onConfirm(selected);
    onOpenChange(false);
    setSelected([]);
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSelected([]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Select Subtasks</DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          <div className="text-sm text-muted-foreground">
            Select the subtasks you want to manage for this project.
          </div>
          <div>
            <label className="text-sm font-medium">Available subtasks</label>
            <div className="grid grid-cols-2 gap-2 max-h-64 overflow-auto mt-2 border rounded-md p-3">
              {SUPERVISOR_PHASES.map((phase) => (
                <label key={phase} className="flex items-center gap-2 text-sm">
                  <Checkbox checked={selected.includes(phase)} onCheckedChange={() => toggle(phase)} />
                  <span>{(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}</span>
                </label>
              ))}
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleCancel}>Cancel</Button>
            <Button disabled={selected.length === 0} onClick={handleConfirm}>
              Confirm Selection ({selected.length} selected)
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
