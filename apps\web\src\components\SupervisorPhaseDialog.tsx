import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { SUPERVISOR_SUBTASK_LABELS, SupervisorSubTask } from '@/types/project';
import { useRef } from 'react';

export type PhaseStatus = 'not_started' | 'booked' | 'pending' | 'in_progress' | 'complete' | 'defect';
export type PhaseStateRecord = Record<string, {
  status: PhaseStatus;
  contractor?: string;
  startDate?: string;
  endDate?: string;
}>; // key is SupervisorSubTask

const STATUS_LABEL: Record<PhaseStatus, string> = {
  not_started: 'Not Started',
  booked: 'Booked',
  pending: 'Pending',
  in_progress: 'In Progress',
  complete: 'Complete',
  defect: 'Defect',
};

const STATUS_TONE: Record<PhaseStatus, string> = {
  not_started: 'bg-muted text-muted-foreground',
  booked: 'bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300',
  pending: 'bg-amber-100 text-amber-700 dark:bg-amber-950 dark:text-amber-300',
  in_progress: 'bg-sky-100 text-sky-700 dark:bg-sky-950 dark:text-sky-300',
  complete: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-950 dark:text-emerald-300',
  defect: 'bg-rose-100 text-rose-700 dark:bg-rose-950 dark:text-rose-300',
};

export function getAllowedTransitions(current: PhaseStatus): PhaseStatus[] {
  // Allow users to select any status, no sequential restriction
  const allStatuses: PhaseStatus[] = ['not_started', 'booked', 'pending', 'in_progress', 'complete', 'defect'];
  // Return all statuses except the current one
  return allStatuses.filter(status => status !== current);
}

export function SupervisorPhaseDialog({
  open,
  onOpenChange,
  selectedPhases,
  phaseStates,
  onChange,
  onTransition,
  onUpdate,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  selectedPhases: SupervisorSubTask[];
  phaseStates: PhaseStateRecord;
  onChange: (next: PhaseStateRecord) => void;
  onTransition?: (phaseKey: SupervisorSubTask, to: PhaseStatus, additionalData?: { contractor?: string; startDate?: string; endDate?: string }) => void;
  onUpdate?: (phaseKey: SupervisorSubTask, data: { contractor?: string; startDate?: string; endDate?: string }) => void;
}) {
  const updateTimeouts = useRef<Record<string, NodeJS.Timeout>>({});
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Update Subtask Status</DialogTitle>
        </DialogHeader>

        <div className="space-y-2 max-h-[60vh] overflow-auto pr-1">
          {selectedPhases.length === 0 && (
            <div className="text-sm text-muted-foreground">No phases selected.</div>
          )}
          {selectedPhases.map((phase) => {
            const current = (phaseStates[phase]?.status ?? 'not_started') as PhaseStatus;
            const allowed = getAllowedTransitions(current);
            const phaseData = phaseStates[phase] || { status: 'not_started' as PhaseStatus };

            const updatePhaseData = (updates: Partial<typeof phaseData>) => {
              const newPhaseStates = {
                ...phaseStates,
                [phase]: { ...phaseData, ...updates }
              };
              onChange(newPhaseStates);

              // Only call the update API if values have actually changed
              const hasChanges = (
                (updates.contractor !== undefined && updates.contractor !== phaseData.contractor) ||
                (updates.startDate !== undefined && updates.startDate !== phaseData.startDate) ||
                (updates.endDate !== undefined && updates.endDate !== phaseData.endDate)
              );

              if (hasChanges) {
                // Clear existing timeout for this phase
                if (updateTimeouts.current[phase]) {
                  clearTimeout(updateTimeouts.current[phase]);
                }

                // Set new timeout to debounce API calls
                updateTimeouts.current[phase] = setTimeout(() => {
                  onUpdate?.(phase, {
                    contractor: updates.contractor,
                    startDate: updates.startDate,
                    endDate: updates.endDate
                  });
                  delete updateTimeouts.current[phase];
                }, 500); // 500ms debounce
              }
            };

            return (
              <div key={phase} className="p-4 rounded border space-y-3">
                <div className="flex items-center gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate" title={(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}>
                      {(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}
                    </div>
                    <div className="text-xs text-muted-foreground">Current: {STATUS_LABEL[current]}</div>
                  </div>
                  <Badge className={`shrink-0 ${STATUS_TONE[current]}`}>{STATUS_LABEL[current]}</Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="sm" variant="outline">Change status</Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {allowed.map((to) => (
                        <DropdownMenuItem key={to} onClick={() => {
                          updatePhaseData({ status: to });
                          onTransition?.(phase, to, {
                            contractor: phaseData.contractor,
                            startDate: phaseData.startDate,
                            endDate: phaseData.endDate
                          });
                        }}>
                          {STATUS_LABEL[to]}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* New input fields */}
                <div className="grid grid-cols-3 gap-3">
                  <div className="space-y-1">
                    <Label htmlFor={`contractor-${phase}`} className="text-xs">Contractor</Label>
                    <Input
                      id={`contractor-${phase}`}
                      placeholder="Enter contractor"
                      value={phaseData.contractor || ''}
                      onChange={(e) => updatePhaseData({ contractor: e.target.value })}
                      className="h-8 text-xs"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`start-date-${phase}`} className="text-xs">Start Date</Label>
                    <Input
                      id={`start-date-${phase}`}
                      type="date"
                      value={phaseData.startDate || ''}
                      onChange={(e) => updatePhaseData({ startDate: e.target.value })}
                      className="h-8 text-xs"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor={`end-date-${phase}`} className="text-xs">End Date</Label>
                    <Input
                      id={`end-date-${phase}`}
                      type="date"
                      value={phaseData.endDate || ''}
                      onChange={(e) => updatePhaseData({ endDate: e.target.value })}
                      className="h-8 text-xs"
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

